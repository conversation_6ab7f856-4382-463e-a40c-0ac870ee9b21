import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, throwError } from 'rxjs';
import { tap, catchError, map } from 'rxjs/operators';
import { Client, CreateClientDto, UpdateClientDto } from '../Model/Client';

@Injectable({
  providedIn: 'root'
})
export class ClientService {
  private baseUrl: string = 'https://localhost:5001/api/Client/';
  private currentClientSubject: BehaviorSubject<Client | null>;
  public currentClient$: Observable<Client | null>;
  dataChange = new BehaviorSubject<Client[]>([]);
  dialogData!: Client;
  isTblLoading = true;

  constructor(private http: HttpClient, private router: Router) {
    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());
    this.currentClient$ = this.currentClientSubject.asObservable();
  }

  private getClientFromStorage(): Client | null {
    const clientData = localStorage.getItem('currentClient');
    return clientData ? JSON.parse(clientData) : null;
  }

  private getHeaders(): HttpHeaders {
    const token = localStorage.getItem('auth_token');
    return new HttpHeaders({
      'Content-Type': 'application/json',
      ...(token ? { 'Authorization': `Bearer ${token}` } : {})
    });
  }

  // Récupérer tous les clients
  getAllClients(): Observable<Client[]> {
    this.isTblLoading = true;
    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(
      tap(clients => {
        this.isTblLoading = false;
        this.dataChange.next(clients);
      }),
      catchError(error => {
        this.isTblLoading = false;
        return this.handleError(error);
      })
    );
  }

  // Récupérer un client par son ID
  getClientById(id: string): Observable<Client> {
    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(
      tap(client => {
        localStorage.setItem('currentClient', JSON.stringify(client));
        this.currentClientSubject.next(client);
      }),
      catchError(this.handleError)
    );
  }

  // Créer un nouveau client
  createClient(client: CreateClientDto): Observable<Client> {
    // if (!client) {
    //   return throwError(() => new Error('Les données du client sont requises'));
    // }

    // if (!client.code || client.code.trim() === '') {
    //   return throwError(() => new Error('Le code client est requis'));
    // }

    const clientData: CreateClientDto = {
      code: client.code.trim(),
      syntax: client.syntax?.trim(),
      matFiscal: client.matFiscal?.trim(),
      email: client.email?.trim(),
      telephone: client.telephone?.trim()
    };

    return this.http.post<Client>(this.baseUrl, clientData, {
      headers: this.getHeaders(),
      observe: 'response'
    }).pipe(
      map((response: any) => {
        const newClient: Client = response.body;
        if (!newClient) {
          throw new Error('Aucune donnée reçue du serveur');
        }
        return newClient;
      }),
      tap((newClient: Client) => {
        const currentData = this.dataChange.value;
        this.dataChange.next([...currentData, newClient]);
      }),
      catchError(this.handleCreateError)
    );
  }

  // Mettre à jour un client
  updateClient(id: string, client: UpdateClientDto): Observable<void> {
    if (!id || !client) {
      return throwError(() => new Error('ID et données client requis'));
    }

    const cleanedClient: UpdateClientDto = {};
    Object.keys(client).forEach(key => {
      if (key !== 'id' && client[key as keyof UpdateClientDto]?.toString().trim()) {
        cleanedClient[key as keyof UpdateClientDto] = client[key as keyof UpdateClientDto];
      }
    });

    if (Object.keys(cleanedClient).length === 0) {
      return throwError(() => new Error('Aucune donnée à mettre à jour'));
    }

    return this.http.put<void>(`${this.baseUrl}${id}`, cleanedClient, { 
      headers: this.getHeaders()
    }).pipe(
      tap(() => {
        const currentData = this.dataChange.value;
        const index = currentData.findIndex(c => c.id === id);
        if (index !== -1) {
          currentData[index] = { ...currentData[index], ...cleanedClient };
          this.dataChange.next([...currentData]);
        }
      }),
      catchError(this.handleError)
    );
  }

  // Supprimer un client
  deleteClient(id: string): Observable<void> {
    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(
      tap(() => {
        const currentData = this.dataChange.value;
        this.dataChange.next(currentData.filter(client => client.id !== id));
      }),
      catchError(this.handleError)
    );
  }

  // Supprimer plusieurs clients
  deleteSelectedClients(ids: string[]): Observable<any> {
    if (!ids || ids.length === 0) {
      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));
    }

    return this.http.delete(`${this.baseUrl}delete-selected`, {
      headers: this.getHeaders(),
      body: ids
    }).pipe(
      tap(() => {
        const currentData = this.dataChange.value;
        this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));
      }),
      catchError(this.handleError)
    );
  }

  // Effacer le client courant
  clearCurrentClient(): void {
    localStorage.removeItem('currentClient');
    this.currentClientSubject.next(null);
  }

  get data(): Client[] {
    return this.dataChange.value;
  }

  getDialogData() {
    return this.dialogData;
  }

  // Gestionnaire d'erreur spécialisé pour la création
  private handleCreateError(error: HttpErrorResponse) {
    let errorMessage = 'Erreur lors de la création du client';
    const apiError = error.error?.message || error.error?.title || error.error?.error;

    if (apiError) {
      errorMessage = `Erreur de création: ${apiError}`;
    } else if (error.status === 0) {
      errorMessage = 'Impossible de se connecter au serveur';
    } else if (error.status === 400) {
      errorMessage = 'Données invalides';
      if (error.error?.errors) {
        const validationErrors = Object.values(error.error.errors).flat();
        errorMessage += ` Détails: ${validationErrors.join(', ')}`;
      }
    } else if (error.status === 401) {
      errorMessage = 'Session expirée';
      this.router.navigate(['/login']);
    } else if (error.status === 409) {
      errorMessage = 'Un client avec ce code existe déjà';
    }

    return throwError(() => new Error(errorMessage));
  }

  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'Une erreur est survenue';
    const apiError = error.error?.message || error.error?.title;

    if (apiError) {
      errorMessage = apiError;
    } else if (error.status === 0) {
      errorMessage = 'Impossible de se connecter au serveur';
    } else if (error.status === 401) {
      errorMessage = 'Non autorisé';
      this.router.navigate(['/login']);
    } else if (error.status === 404) {
      errorMessage = 'Client non trouvé';
    } else if (error.status === 409) {
      errorMessage = 'Conflit - le client existe déjà';
    }

    return throwError(() => new Error(errorMessage));
  }
}