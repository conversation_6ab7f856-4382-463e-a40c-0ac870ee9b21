import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Component, Inject } from '@angular/core';
import { ClientService } from '../../services/client.service';
import { UntypedFormControl, Validators, UntypedFormGroup, UntypedFormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { Client, ClientModel, CreateClientDto, UpdateClientDto } from '../../Model/Client';
import { MatCardModule } from '@angular/material/card';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { CommonModule } from '@angular/common';

export interface DialogData {
  id: string;
  action: string;
  client: Client;
}

@Component({
  selector: 'app-form-client',
  templateUrl: './form-client.component.html',
  styleUrls: ['./form-client.component.scss'],
  standalone: true,
  imports: [
    MatButtonModule,
    MatIconModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatCardModule,
    CommonModule,
  ],
})
export class FormClientComponent {
  action: string;
  dialogTitle?: string;
  isDetails = false;
  clientForm?: UntypedFormGroup;
  client: ClientModel;

  constructor(
    public dialogRef: MatDialogRef<FormClientComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData,
    public clientService: ClientService,
    private fb: UntypedFormBuilder
  ) {
    // Set the defaults
    this.action = data.action;
    if (this.action === 'edit') {
      this.isDetails = false;
      this.dialogTitle = `Modifier: ${data.client.code}`;
      this.client = new ClientModel(data.client);
      this.clientForm = this.createClientForm();
    } else if (this.action === 'details') {
      this.client = new ClientModel(data.client);
      this.isDetails = true;
    } else {
      this.isDetails = false;
      this.dialogTitle = 'Nouveau Client';
      const blankObject = {} as Client;
      this.client = new ClientModel(blankObject);
      this.clientForm = this.createClientForm();
    }
  }

  formControl = new UntypedFormControl('', [
    Validators.required,
  ]);

  getErrorMessage() {
    return this.formControl.hasError('required')
      ? 'Champ requis'
      : this.formControl.hasError('email')
      ? 'Format d\'email invalide'
      : '';
  }

  createClientForm(): UntypedFormGroup {
    return this.fb.group({
      id: [this.client.id],
      code: [this.client.code, [Validators.required, Validators.minLength(2), Validators.maxLength(20)]],
      syntax: [this.client.syntax, [Validators.maxLength(100)]],
      matFiscal: [this.client.matFiscal, [Validators.maxLength(50)]], // Suppression de la validation de format
      email: [this.client.email, [Validators.maxLength(100)]], // Suppression de la validation email
      telephone: [this.client.telephone, [this.validateTelephone]],
    });
  }

  submit() {
    if (this.clientForm?.valid) {
      if (this.action === 'add') {
        this.confirmAdd();
      } else if (this.action === 'edit') {
        this.confirmEdit();
      }
    }
  }

  onNoClick(): void {
    this.dialogRef.close();
  }

public confirmAdd(): void {
    if (this.clientForm?.valid) {
      const formValue = this.clientForm.getRawValue();

      // Validation supplémentaire côté client
      // if (!formValue || !formValue.description || formValue.prixUnitaireHT < 0) {
      //   console.error('Données de formulaire invalides:', formValue);
      //   return;
      // }
      // Validation côté client avant envoi
      if (!formValue.code || formValue.code.trim().length < 2) {
        alert('Le code client est requis et doit contenir au moins 2 caractères');
        return;
      }

      const createDto: CreateClientDto = {
        code: formValue.code.trim(),
        syntax: formValue.syntax?.trim() || undefined,
        matFiscal: formValue.matFiscal?.trim() || undefined,
        email: formValue.email?.trim() || undefined,
        telephone: formValue.telephone?.trim() || undefined
      };

      // Nettoyer les valeurs vides et undefined
      Object.keys(createDto).forEach(key => {
        const value = createDto[key as keyof CreateClientDto];
        if (value === '' || value === null || value === undefined) {
          delete createDto[key as keyof CreateClientDto];
        }
      });

      // Validation finale - s'assurer que le code est présent
      if (!createDto.code) {
        alert('Le code client est requis');
        return;
      }

      console.log('=== DONNÉES À ENVOYER ===');
      console.log('Valeurs du formulaire brutes:', formValue);
      console.log('CreateDto final:', createDto);
      console.log('CreateDto stringifié:', JSON.stringify(createDto, null, 2));
      console.log('Nombre de propriétés:', Object.keys(createDto).length);
      console.log('Propriétés présentes:', Object.keys(createDto));
      console.log('========================');

      this.clientService.createClient(createDto).subscribe({
        next: (result: Client) => {
          console.log('Client créé avec succès dans le composant:', result);
          this.dialogRef.close(result);
        },
        error: (error: any) => {
          console.error('=== ERREUR COMPOSANT ===');
          console.error('Erreur complète:', error);
          console.error('Message:', error.message);
          console.error('Status:', error.status);
          console.error('Error object:', error.error);
          console.error('=======================');

          // Afficher les détails de validation si disponibles
          let errorMessage = error.message || 'Erreur lors de la création';
          if (error.error && error.error.errors) {
            console.error('Erreurs de validation détaillées:', error.error.errors);
            const validationErrors = Object.entries(error.error.errors)
              .map(([field, messages]: [string, any]) => `${field}: ${Array.isArray(messages) ? messages.join(', ') : messages}`)
              .join('\n');
            errorMessage += `\n\nDétails de validation:\n${validationErrors}`;
          }

          alert(errorMessage);
        }
      });
    } else {
      console.error('Formulaire invalide:', this.clientForm?.errors);
      alert('Veuillez corriger les erreurs dans le formulaire');
    }
  }

  public confirmEdit(): void {
    if (this.clientForm?.valid) {
      const formValue = this.clientForm.getRawValue();

      // Validation supplémentaire côté client
      if (!formValue.code || formValue.code.trim() === '') {
        console.error('Code client requis');
        alert('Le code client est requis');
        return;
      }

      // Créer l'UpdateClientDto en excluant explicitement l'id
      const updateDto: UpdateClientDto = {};

      // Ajouter seulement les champs définis dans UpdateClientDto (pas l'id)
      // Validation et nettoyage plus strict
      if (formValue.code !== undefined && formValue.code !== null && formValue.code.toString().trim() !== '') {
        updateDto.code = formValue.code.toString().trim();
      }
      if (formValue.syntax !== undefined && formValue.syntax !== null && formValue.syntax.toString().trim() !== '') {
        updateDto.syntax = formValue.syntax.toString().trim();
      }
      if (formValue.matFiscal !== undefined && formValue.matFiscal !== null && formValue.matFiscal.toString().trim() !== '') {
        updateDto.matFiscal = formValue.matFiscal.toString().trim();
      }
      if (formValue.email !== undefined && formValue.email !== null && formValue.email.toString().trim() !== '') {
        updateDto.email = formValue.email.toString().trim();
      }
      if (formValue.telephone !== undefined && formValue.telephone !== null && formValue.telephone.toString().trim() !== '') {
        updateDto.telephone = formValue.telephone.toString().trim();
      }

      console.log('FormValue complet:', formValue);
      console.log('UpdateDto créé (sans ID):', updateDto);
      console.log('ID du client (sera dans URL):', this.client.id);

      // Vérifier qu'au moins un champ est fourni
      if (Object.keys(updateDto).length === 0) {
        alert('Aucune modification détectée');
        return;
      }

      // Validation supplémentaire des données
      if (updateDto.code && updateDto.code.length < 2) {
        alert('Le code client doit contenir au moins 2 caractères');
        return;
      }
      if (updateDto.email && updateDto.email.includes(' ')) {
        alert('L\'email ne doit pas contenir d\'espaces');
        return;
      }

      console.log('=== COMPOSANT FORM CLIENT ===');
      console.log('Client complet:', this.client);
      console.log('ID du client:', this.client.id);
      console.log('Type de l\'ID:', typeof this.client.id);
      console.log('UpdateDto à envoyer:', updateDto);
      console.log('UpdateDto stringifié:', JSON.stringify(updateDto, null, 2));
      console.log('Clés de updateDto:', Object.keys(updateDto));
      console.log('Valeurs de updateDto:', Object.values(updateDto));
      console.log('============================');

      this.clientService.updateClient(this.client.id, updateDto).subscribe({
        next: () => {
          console.log('Client mis à jour avec succès');
          this.dialogRef.close(true);
        },
        error: (error: any) => {
          console.error('=== ERREUR DÉTAILLÉE ===');
          console.error('Erreur complète:', error);
          console.error('Status:', error.status);
          console.error('Error object:', error.error);
          console.error('Error message:', error.message);
          console.error('Error name:', error.name);
          console.error('URL:', error.url);
          console.error('========================');

          let errorMessage = 'Erreur lors de la modification du client';
          if (error.error && error.error.message) {
            errorMessage = error.error.message;
          } else if (error.message) {
            errorMessage = error.message;
          }

          // Utiliser une alerte ou un snackbar selon le type d'erreur
          if (errorMessage.includes('validation') || errorMessage.includes('Détails:')) {
            // Pour les erreurs de validation, afficher dans la console et une alerte simple
            console.warn('Erreurs de validation détaillées:', errorMessage);
            alert('Erreurs de validation détectées. Veuillez vérifier les champs du formulaire.\n\nConsultez la console pour plus de détails.');
          } else {
            alert(errorMessage);
          }
        }
      });
    } else {
      console.error('Formulaire invalide:', this.clientForm?.errors);
      alert('Veuillez corriger les erreurs dans le formulaire');
    }
  }

  // Validation personnalisée pour le matricule fiscal tunisien
  validateMatFiscal(control: UntypedFormControl) {
    const value = control.value?.trim();
    if (!value) return null;

    // Format: 7 chiffres + 3 lettres + 3 chiffres (ex: 1234567ABC123)
    // Ou format simplifié: au moins 8 caractères alphanumériques
    const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;
    return matFiscalPattern.test(value.toUpperCase()) ? null : { invalidMatFiscal: true };
  }

  // Validation personnalisée pour le téléphone
  validateTelephone(control: UntypedFormControl) {
    const value = control.value?.trim();
    if (!value) return null;

    // Accepte différents formats de téléphone (plus flexible)
    const phonePattern = /^[+]?[0-9\s\-\(\)\.]{8,20}$/;
    return phonePattern.test(value) ? null : { invalidPhone: true };
  }

  // Méthode pour obtenir les messages d'erreur spécifiques
  getFieldErrorMessage(fieldName: string): string {
    const field = this.clientForm?.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} est requis`;
      if (field.errors['minlength']) return `${fieldName} trop court`;
      if (field.errors['maxlength']) return `${fieldName} trop long`;
      if (field.errors['invalidPhone']) return 'Format de téléphone invalide';
      // Suppression des messages pour email et matricule fiscal
    }
    return '';
  }
}
