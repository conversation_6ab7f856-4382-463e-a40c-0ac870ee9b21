export interface Client {
  id: string;
  code: string;
  syntax?: string;
  matFiscal?: string;
  email?: string;
  telephone?: string;
  devis?: any[]; // Si vous avez un modèle <PERSON>
}

export interface CreateClientDto {
  code: string;
  syntax?: string;
  matFiscal?: string;
  email?: string;
  telephone?: string;
}

export interface UpdateClientDto {
  code?: string;
  syntax?: string;
  matFiscal?: string;
  email?: string;
  telephone?: string;
}

export class ClientModel implements Client {
  id: string;
  code: string;
  syntax: string;
  matFiscal: string;
  email: string;
  telephone: string;
  devis: any[];

  constructor(client: Partial<Client> = {}) {
    this.id = client.id || this.getRandomID();
    this.code = client.code || '';
    this.syntax = client.syntax || '';
    this.matFiscal = client.matFiscal || '';
    this.email = client.email || '';
    this.telephone = client.telephone || '';
    this.devis = client.devis || [];
  }

  public getRandomID(): string {
    const S4 = () => {
      return ((1 + Math.random()) * 0x10000) | 0;
    };
    return (S4() + S4()).toString();
  }

  // Méthode pour créer un client à partir d'un DTO
  static fromCreateDto(dto: CreateClientDto): ClientModel {
    return new ClientModel({
      id: this.generateId(), // Génère un nouvel ID
      code: dto.code,
      syntax: dto.syntax || '',
      matFiscal: dto.matFiscal || '',
      email: dto.email || '',
      telephone: dto.telephone || ''
    });
  }

  // Méthode pour générer un ID (à adapter selon vos besoins)
  private static generateId(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  // Méthodes utilitaires
  public get fullIdentifier(): string {
    return `${this.code} - ${this.syntax}`;
  }

//   public isValid(): boolean {
//     return !!this.code.trim();
//   }
}

// Interface de base pour Devis (à compléter selon votre modèle)
